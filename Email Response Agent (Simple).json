{"name": "Email Categorisation Agent (Simple)", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "simple": false, "filters": {}, "options": {"downloadAttachments": false}}, "type": "n8n-nodes-base.gmailTrigger", "typeVersion": 1.2, "position": [1380, -500], "id": "ffc6a0c5-48ea-469d-a829-d9b52f4f1e70", "name": "<PERSON><PERSON>", "credentials": {"gmailOAuth2": {"id": "YOUR_GMAIL_CREDENTIAL_ID", "name": "Gmail Integration"}}}, {"parameters": {"inputText": "={{ $json.text }}", "categories": {"categories": [{"category": "Spam", "description": "The message is a generic sales pitch, mass outreach, or irrelevant to your business. These messages are typically unsolicited and often promote unrelated services, products, or offers. They tend to be templated, impersonal, or automated, with no specific reference to your business, your services, or recent interactions. Common signs include pushy sales language, overuse of promotional phrases, and little to no alignment with your industry or offerings. Do not classify a message as Spam if the sender shows genuine interest in your services, even if the tone is somewhat promotional."}, {"category": "<PERSON><PERSON>", "description": "An Urgent email is time-sensitive and requires immediate attention or action. This type of message may include deadlines (for example, “please respond by the end of today”), critical service issues (such as account access problems, major technical failures), or escalations that pose a risk to business continuity or customer experience if not addressed quickly. Look for clear signals of urgency in the language, such as phrases like “immediately,” “urgent,” or “as soon as possible.” Only classify as Urgent if the content genuinely requires fast intervention."}, {"category": "Customer Service", "description": "A Customer Service email is focused on requesting help with an existing service, product, or account that the sender already has with your business. This includes troubleshooting issues, billing queries, technical support requests, or questions related to account management. Typical examples include customers asking for an invoice copy, help logging into their account, or clarification on a service feature. The key difference between Customer Service and Opportunity is that the sender is already a customer or user, and they need assistance rather than exploring new services."}, {"category": "Sales", "description": "A Sales email is when the sender expresses interest in your services, products, or a potential collaboration. They might ask about pricing, service details, demos, consultations, or next steps to engage your business. These messages indicate potential new business and can include inquiries like “I’m interested in your automation services” or “Can you tell me more about your pricing and process?” The focus is on acquiring new services rather than resolving an issue with an existing one. Sales emails are valuable leads and should be treated as potential growth opportunities."}, {"category": "General Response", "description": "A General Response email is a routine, non-urgent message that doesn’t fit into any of the other categories. These messages often involve general acknowledgements, polite follow-ups, or updates that do not contain a clear request, issue, or business inquiry. Examples include simple thank-you messages, confirmations that don’t require action, or general updates where no further engagement is necessary beyond a courteous reply. Use this category when the email is neutral and doesn’t meet the criteria for Spam, Urgent, Customer Service, or Opportunity."}, {"category": "Invoice/Receipt", "description": "An Invoice/Receipt email relates specifically to financial documents or payment confirmations. This category covers messages where the sender is requesting a copy of an invoice or receipt, confirming receipt of an invoice or payment, or sending/forwarding invoices, receipts, or proof of payment. These emails are administrative and transactional in nature, focusing solely on billing or payment documentation. They do not involve technical support, general customer service, or sales inquiries. Use this category when the primary purpose of the email is to handle or request invoices, receipts, or payment confirmations."}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.textClassifier", "typeVersion": 1, "position": [1600, -500], "id": "96c9dc0f-45b4-45d7-946d-41bd7b0e5796", "name": "Email Classifier"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1620, -620], "id": "0a7bc3a7-2a41-4dc3-b828-01f1cab21097", "name": "Classifier Brain", "credentials": {"openAiApi": {"id": "pVebLmNMBp3DGLT3", "name": "Ops Management"}}}, {"parameters": {"operation": "addLabels", "messageId": "={{ $('Gmail Trigger').item.json.id }}", "labelIds": ["SPAM"]}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [760, -240], "id": "bad37b68-2af7-492c-a22f-825defda9b15", "name": "<PERSON> as <PERSON>m", "webhookId": "bcebb951-f1ce-4aca-9cf1-68650c243e5d", "credentials": {"gmailOAuth2": {"id": "YOUR_GMAIL_CREDENTIAL_ID", "name": "Gmail Integration"}}}, {"parameters": {"content": "## **Mark as Spam**", "height": 220, "width": 200, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [720, -300], "id": "23fc40a3-2a64-44cf-96fe-b5b2faccccb7", "name": "Sticky Note1"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1000, -100], "id": "d6c5e538-4039-4b69-948b-635a187660c1", "name": "Response Brain", "credentials": {"openAiApi": {"id": "pVebLmNMBp3DGLT3", "name": "Ops Management"}}}, {"parameters": {"promptType": "define", "text": "={{ $('All Snippets').item.json['all snippets'] }}", "messages": {"messageValues": [{"message": "=## Task\nYou are a professional email drafting AI.\n\nYour job is to draft a clear, concise, and polite reply to the latest email in an ongoing conversation thread. Use the full context of the thread to ensure the response is relevant and consistent with prior messages.\n\nYou are doing this on behalf of <PERSON>.\n\n---\n\n## Style & Tone Guidelines:\n- Maintain a professional, approachable, and confident tone.  \n- Keep responses concise and to the point (avoid unnecessary filler).  \n- Ensure language is consistent with a businesslike tone of voice that is proactive and solutions-focused.  \n- Minimise the use of the word \"I\" wherever possible (reframe sentences to sound collaborative or neutral).  \n- Ensure clarity on next steps or provide helpful information, depending on the nature of the thread.\n\n---\n\n## Output Format:\nRespond with only the draft email body text (no greetings or sign-offs).\n\n---\n\n## 📩 Conversation Thread:\n{{ $('All Snippets').item.json['all snippets'] }}"}]}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [1000, -240], "id": "1f1ed289-5930-42c4-829f-ee64e5d55240", "name": "Draft Response"}, {"parameters": {"operation": "reply", "messageId": "={{ $('New Email').item.json.id }}", "emailType": "text", "message": "={{ $('Draft Response').item.json.text }}\n\nBest regards,\n[Your Name]", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [1160, -120], "id": "a74ed4b9-2456-4f70-9a45-0d890d7ddfc4", "name": "Send URGENT Response", "webhookId": "5cac925c-bc30-4c5b-8283-c0e674ce80f2", "credentials": {"gmailOAuth2": {"id": "YOUR_GMAIL_CREDENTIAL_ID", "name": "Gmail Integration"}}}, {"parameters": {"content": "## **Urgent/General Email Response**", "height": 340, "width": 400, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [960, -300], "id": "14dfb394-0b4a-4707-a016-997cd4f93eb6", "name": "<PERSON><PERSON>"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "GPT-4.1-MINI"}, "messages": {"values": [{"content": "=## 🧠 Task\nYou are a customer support response AI.\n\nYour job is to review the customer's request, identify the key question or issue, and respond only if the information required is fully available within your provided context or the linked knowledge base (vector database).\n\nYou must strictly use the exact wording provided in the knowledge base or your prompt (within authorised guidelines). Do not generate new answers, rephrase, or guess.\n\nIf step-by-step guidance, troubleshooting steps, or instructions exist in the authorised knowledge base, you MUST provide them exactly as written. Do not default to escalation unless the knowledge base explicitly instructs you to escalate for this scenario.\n\nIf you do not have sufficient authorised information to provide a valid response, output:\n\nRequires Human\n\n---\n\n## 🔍 Process:\n\n1. Analyse the Request:\n   - Understand the customer’s question, issue, or request.\n\n2. Check Existing Context:\n   - See if the answer (especially troubleshooting steps or guidance) is fully available within your current prompt/context.\n\n3. Search Knowledge Base:\n   - If needed, search the provided knowledge base (vector database) to find the exact, authorised answer.\n\n4. Respond:\n   - Use only the exact, authorised wording (especially step-by-step instructions, if available).\n   - If multiple knowledge base entries are relevant, select the most specific and relevant one.\n\n5. Fallback:\n   - If no complete authorised answer is available, output:\n     Requires Human\n\n---\n\n## 🚫 Strict Rules:\n\n- Do not create new wording outside of the authorised material.\n- Do not make assumptions, predictions, or provide general advice unless explicitly stated in the authorised guidelines.\n- Do not combine unauthorised knowledge with authorised content.\n- Do not escalate, apologise, or say \"you will be contacted\" unless the knowledge base explicitly says to do so for this case.\n- Always prioritise providing concrete steps or guidance if available in the knowledge base.\n\n---\n\n## ✅ Output Format:\n\nIf a valid response is available, output the full email body as HTML, structured as follows:\n\n<p>Hi customer first name </p>\n\n<p>authorised response</p>\n\n<p>Kind regards,<br>\n\n---\n\nWhere:\n\n- customer_name = Only use their first name (do not include the entire name); use:  \n  `{{ $('New Email').item.json.From }}`\n\n- authorised_response = The exact authorised answer (with HTML formatting if available)\n\nIf no valid response is available, output:\n\nRequires Human\n\n---\n\n📩 Customer Request:\n{{ $('All Snippets1').item.json['all snippets'] }}\n", "role": "assistant"}, {"content": "=Here is the users message:\n{{ $('All Snippets1').item.json['all snippets'] }}"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1420, -240], "id": "efc19835-fbeb-40e5-ae13-6c26f2349d0e", "name": "Response Generator", "credentials": {"openAiApi": {"id": "pVebLmNMBp3DGLT3", "name": "Ops Management"}}}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1420, 60], "id": "3413c2a6-8824-43f6-869a-dfbb7b9cac5a", "name": "Q&A Brain", "credentials": {"openAiApi": {"id": "pVebLmNMBp3DGLT3", "name": "Ops Management"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [1680, 60], "id": "383360c8-d225-4181-9ea6-375c081f5b31", "name": "Small Embeddings", "credentials": {"openAiApi": {"id": "pVebLmNMBp3DGLT3", "name": "Ops Management"}}}, {"parameters": {"pineconeIndex": {"__rl": true, "value": "customer-service", "mode": "list", "cachedResultName": "customer-service"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1.1, "position": [1680, -80], "id": "dc862a16-466e-4551-b3de-2f8f7a47ad3d", "name": "Pinecone Q&A Store", "credentials": {"pineconeApi": {"id": "GFlwfV3RkjziPWtU", "name": "Agera AI Pinecone"}}}, {"parameters": {"description": "Customer service knowledge base to be able to provide responses back to customers"}, "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "typeVersion": 1.1, "position": [1400, -80], "id": "59c54142-993c-4bc7-9828-c642c5c329cf", "name": "QA Vector Store"}, {"parameters": {"operation": "reply", "messageId": "={{ $('New Email').item.json.id }}", "message": "={{ $json.message.content }}\n\n<br> Many Thanks,<br>\nCustomer Support Team", "options": {"replyToSenderOnly": true}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [1760, -240], "id": "a9d63d88-a0c1-48a8-b59d-9eecc5df598c", "name": "Respond to Customer", "webhookId": "b9a38785-8b85-432c-b014-7ee3f1a1ff44", "credentials": {"gmailOAuth2": {"id": "YOUR_GMAIL_CREDENTIAL_ID", "name": "Gmail Integration"}}}, {"parameters": {"content": "## **Customer Support Response**", "height": 500, "width": 560, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1380, -300], "id": "44434871-10a3-41b4-8993-c4c0c5919d1f", "name": "Sticky Note2"}, {"parameters": {"content": "## **Sales Response**", "height": 700, "width": 440, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1960, -300], "id": "a234e5c1-4900-4b69-ae78-02b27d1f2c55", "name": "Sticky Note7"}, {"parameters": {"operation": "reply", "messageId": "={{ $('New Email').item.json.id }}", "message": "={{ $json.text }}\n\n<br>Many Thanks,<br>\nSales Team", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [2180, -100], "id": "8016c226-9d4a-415d-8241-b9e64bb8e685", "name": "Respond to Lead", "webhookId": "1bce6ad4-1d02-47d7-bfbb-a5bdf0f8e47d", "credentials": {"gmailOAuth2": {"id": "YOUR_GMAIL_CREDENTIAL_ID", "name": "Gmail Integration"}}}, {"parameters": {"promptType": "define", "text": "=## 🧠 Task\nYou are a notification AI.\n\nYour job is to review the incoming opportunity/inquiry and provide a concise summary for the internal team to be notified about the new lead.\n\nThe summary should:\n\n- Clearly state the nature of the inquiry/opportunity.  \n- Include any service(s) mentioned by the sender.  \n- Note the sender’s name and company if available.  \n- Be brief, actionable, and easy to scan quickly.\n\n---\n\n## ✅ Output Format:\n\nNew Opportunity Notification:\n\n- Sender: Customer Name\n- Service(s) Mentioned: Services offered in email\n- Summary: summary of customer's message plus the email response\n- Suggested Next Step: Review and follow up as needed. Lead is classified as (Hot/Warm/Cold).\n\n---\n\n## 🚫 Strict Rules:\n\n- Keep the summary short and clear (1–2 sentences max).  \n- If no company name is available, omit the parentheses.  \n- If no specific service is mentioned, write: “No specific service mentioned.”  \n- Always state the lead classification (Hot, Warm, or Cold).  \n- Do not generate extra commentary—just the notification in the format above.\n\n---\n\n## AI Agent Email to Summarise:\n{{ $('Sales Message').item.json.text }}\n\n---\n##Email chain with customer:\n{{ $('All Snippets').item.json['all snippets'] }}"}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [2040, 80], "id": "f56dfb63-d158-4c88-a276-8b08e8e65e33", "name": "Summarise Request"}, {"parameters": {"select": "channel", "channelId": {"__rl": true, "value": "C08RRA0654G", "mode": "list", "cachedResultName": "email-leads"}, "text": "={{ $json.text }}", "otherOptions": {}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [2200, 220], "id": "692b659c-b7cb-4e4d-ae66-f6a9839171d3", "name": "Notify Team", "webhookId": "c4a2aff5-0989-450d-9808-c6407623d1f7", "credentials": {"slackApi": {"id": "TfkZYwtKVv6KVC62", "name": "Email Agent"}}}, {"parameters": {"promptType": "define", "text": "=## 🧠 Task\nYou are a lead engagement AI.\n\nYour job is to analyse the incoming inquiry and classify it as Cold, Warm, or Hot based on the message content. Then, generate a full HTML-formatted email response based on the classification:\n\n- **Cold Lead:**  \n  Introduce all available services clearly and encourage the recipient to book a session using the provided booking link.\n\n- **Warm/Hot Lead:**  \n  If the inquiry mentions a specific service, focus the reply on that service by providing more detailed information and inviting the recipient to book a session.\n\n---\n\n## 🔍 Services Offered (Dummy Data for Guidance):\n\n- **AI & Automation Strategy Sessions:**  \n  60-minute consultation to identify automation opportunities.  \n  [https://booking.example.com/strategy-session](https://booking.example.com/strategy-session)\n\n- **AI Voice Agent Implementation:**  \n  End-to-end setup of AI phone agents to handle customer inquiries, bookings, and support 24/7.\n\n- **Process Automation:**  \n  Automate repetitive tasks such as invoice processing, CRM updates, and customer follow-ups.\n\n- **Custom AI Chatbots:**  \n  Build and deploy tailored chatbots to engage website visitors and improve lead conversion.\n\n---\n\n## ✅ Output Format:\n\nIf the lead is Cold:\n\n<p>Hi customer first name,</p>\n\n<p>Thanks for reaching out! We offer a range of AI and automation services designed to help streamline your business operations and boost productivity. Here’s an overview of how we can help:</p>\n\n<ul>\n  <li><strong>AI & Automation Strategy Sessions:</strong> 60-minute consultation to identify and prioritise automation opportunities tailored to your business.</li>\n  <li><strong>AI Voice Agent Implementation:</strong> AI phone agents that manage bookings, answer common questions, and provide support 24/7.</li>\n  <li><strong>Process Automation:</strong> Automate tasks like invoicing, CRM updates, and email follow-ups to save time and reduce errors.</li>\n  <li><strong>Custom AI Chatbots:</strong> Build and deploy chatbots that engage your website visitors and convert more leads.</li>\n</ul>\n\n<p>If you’d like to explore how these services can fit your needs, please book a free discovery call here:  \n<a href=\"https://booking.example.com/discovery-call\" target=\"_blank\">Book a Discovery Call</a></p>\n\n<p>Looking forward to connecting!</p>\n\nIf the lead is Warm/Hot and has mentioned a specific service (e.g., AI Voice Agent):\n\n<html>\n<p>Hi customer first name,</p>\n\n<p>Great to hear you're interested in our mentioned service! Here’s a bit more detail to help you get started:</p>\n\n<p><strong> service description</strong></p>\n\n<p>We’d love to discuss how this can be tailored to your business. Please book a time that suits you using the link below:</p>\n\n<p><a href=\"https://booking.example.com/strategy-session\" target=\"_blank\">Schedule a Strategy Session</a></p>\n\n<p>Looking forward to working with you!</p>\n</html>\n\n🛠 Service Descriptions (for AI use):\n\nAI & Automation Strategy Sessions:\n60-minute consultation to uncover and prioritise automation opportunities tailored to your workflows and goals.\n\nAI Voice Agent Implementation:\nFull-service AI voice agents that can answer inbound calls, manage bookings, and provide support around the clock, improving customer satisfaction and saving your team time.\n\nProcess Automation:\nAutomate repetitive tasks such as invoice processing, CRM data entry, and customer follow-ups to boost operational efficiency.\n\nCustom AI Chatbots:\nBespoke AI chatbots are designed to engage website visitors, answer questions, and drive conversions, fully integrated into your existing systems.\n\n🚫 Strict Rules:\n\n- Classify the lead as Cold, Warm, or Hot based on their message intent and tone.\n- For Cold leads: always include the full service overview + booking link.\n- For Warm/Hot leads: if a service is mentioned, focus the reply on that service with the relevant description + booking link.\n- Replace customer's first name with the recipient’s first name. You can find the customer's first name within here: {{ $('New Email').item.json.From }}. If not check the signoff of their email and If the name is still unavailable, default to: Hi there,\n- Replace the mentioned service and service description based on the specific service mentioned in the inquiry.\n- Always output raw HTML but no '''' or HTML\n\n📩 Inquiry to Analyse:\n{{ $('All Snippets').item.json['all snippets'] }}"}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [2040, -240], "id": "8c2d4541-afb8-451c-890d-28d237d06d9f", "name": "Sales Message"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [2020, 240], "id": "e92f9380-142c-4586-bd25-de10299a9b53", "name": "Sum Request Brain", "credentials": {"openAiApi": {"id": "pVebLmNMBp3DGLT3", "name": "Ops Management"}}}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [2040, -80], "id": "7177e16f-c006-42a9-998f-c038df680fe8", "name": "Sales Brain", "credentials": {"openAiApi": {"id": "pVebLmNMBp3DGLT3", "name": "Ops Management"}}}, {"parameters": {"content": "## **Invoice/Receipt**", "height": 400, "width": 220, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2420, -300], "id": "debef20f-99e1-4be5-81d9-dfe386ffb969", "name": "Sticky Note9"}, {"parameters": {"inputDataFieldName": "attachment_0", "name": "={{ $json.headers.subject }} - {{ $json.headers.date }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "value": "YOUR_GOOGLE_DRIVE_FOLDER_ID", "mode": "list", "cachedResultName": "Your Folder Name", "cachedResultUrl": "https://drive.google.com/drive/folders/YOUR_GOOGLE_DRIVE_FOLDER_ID"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [2460, -60], "id": "5003fffa-8862-4b6d-90ea-5dff9bd070cd", "name": "Google Drive", "credentials": {"googleDriveOAuth2Api": {"id": "YOUR_GOOGLE_DRIVE_CREDENTIAL_ID", "name": "Google Drive Account"}}}, {"parameters": {"operation": "get", "messageId": "={{ $('Gmail Trigger').item.json.id }}", "simple": false, "options": {"downloadAttachments": true}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [2460, -240], "id": "7f03ea59-913c-4a0d-81a6-240d65f8637a", "name": "Get Attachment", "webhookId": "2c6e07fa-46eb-4ff9-97d2-df6ba9465e14", "credentials": {"gmailOAuth2": {"id": "YOUR_GMAIL_CREDENTIAL_ID", "name": "Gmail Integration"}}}, {"parameters": {"content": "## **Email Response Agent**", "height": 320, "width": 640, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1340, -660], "id": "0a9dcb85-cea6-4029-b849-fa39a6a95e65", "name": "Sticky Note3"}, {"parameters": {"mode": "insert", "pineconeIndex": {"__rl": true, "value": "customer-service", "mode": "list", "cachedResultName": "customer-service"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1.1, "position": [1520, 280], "id": "6d5ff4b4-eaba-4750-bad8-f412ed5ea90c", "name": "Pinecone Vector Store", "credentials": {"pineconeApi": {"id": "GFlwfV3RkjziPWtU", "name": "Agera AI Pinecone"}}}, {"parameters": {"dataType": "binary", "options": {}}, "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [1480, 460], "id": "0b46cfad-7e6f-4d64-bd17-56200314bc34", "name": "Default Data Loader"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [1340, 440], "id": "dce2c22e-d0d2-4321-97b4-8b0ebc5457cc", "name": "Embeddings OpenAI", "credentials": {"openAiApi": {"id": "pVebLmNMBp3DGLT3", "name": "Ops Management"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "typeVersion": 1, "position": [1760, 460], "id": "39a5d4b0-e0fd-4308-8180-e0ffa7b979b1", "name": "Recursive Character Text Splitter"}, {"parameters": {"formTitle": "Knowledge Base Documents", "formFields": {"values": [{"fieldLabel": "Knowledge Base Documents", "fieldType": "file"}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [1320, 280], "id": "c7459362-30e2-4263-9874-8b1085df218e", "name": "On form submission", "webhookId": "badfa579-bc13-4ac4-b4a4-75a5d012edd7"}, {"parameters": {"content": "## **Store Guidance Documents**", "height": 380, "width": 660, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1280, 220], "id": "d8c3c282-fbcf-4f41-990e-7598103e6215", "name": "Sticky Note10"}], "pinData": {}, "connections": {"Gmail Trigger": {"main": [[{"node": "Email Classifier", "type": "main", "index": 0}]]}, "Classifier Brain": {"ai_languageModel": [[{"node": "Email Classifier", "type": "ai_languageModel", "index": 0}]]}, "Email Classifier": {"main": [[{"node": "<PERSON> as <PERSON>m", "type": "main", "index": 0}], [{"node": "Draft Response", "type": "main", "index": 0}], [{"node": "Response Generator", "type": "main", "index": 0}], [{"node": "Sales Message", "type": "main", "index": 0}], [{"node": "Draft Response", "type": "main", "index": 0}], [{"node": "Get Attachment", "type": "main", "index": 0}]]}, "Response Brain": {"ai_languageModel": [[{"node": "Draft Response", "type": "ai_languageModel", "index": 0}]]}, "Draft Response": {"main": [[{"node": "Send URGENT Response", "type": "main", "index": 0}]]}, "Q&A Brain": {"ai_languageModel": [[{"node": "QA Vector Store", "type": "ai_languageModel", "index": 0}]]}, "Small Embeddings": {"ai_embedding": [[{"node": "Pinecone Q&A Store", "type": "ai_embedding", "index": 0}]]}, "Pinecone Q&A Store": {"ai_vectorStore": [[{"node": "QA Vector Store", "type": "ai_vectorStore", "index": 0}]]}, "QA Vector Store": {"ai_tool": [[{"node": "Response Generator", "type": "ai_tool", "index": 0}]]}, "Response Generator": {"main": [[{"node": "Respond to Customer", "type": "main", "index": 0}]]}, "Respond to Lead": {"main": [[{"node": "Summarise Request", "type": "main", "index": 0}]]}, "Summarise Request": {"main": [[{"node": "Notify Team", "type": "main", "index": 0}]]}, "Sales Message": {"main": [[{"node": "Respond to Lead", "type": "main", "index": 0}]]}, "Sum Request Brain": {"ai_languageModel": [[{"node": "Summarise Request", "type": "ai_languageModel", "index": 0}]]}, "Sales Brain": {"ai_languageModel": [[{"node": "Sales Message", "type": "ai_languageModel", "index": 0}]]}, "Get Attachment": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Pinecone Vector Store", "type": "ai_document", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Pinecone Vector Store", "type": "ai_embedding", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "On form submission": {"main": [[{"node": "Pinecone Vector Store", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "fe65025d-5656-43d0-bef6-443bfad7d27e", "meta": {"templateCredsSetupCompleted": true, "instanceId": "8e19fa74944b9dcd6868709031c9aa3bce12c850be41a7bacd11dbae58569d60"}, "id": "T0BYi7uwJkFgI9Xe", "tags": []}