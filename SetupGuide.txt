Lovable Link → [Your deployed app URL will go here]

# Introduction

Why Cosmos Exists

Imagine having access to every piece of information you've ever encountered — videos you've watched, documents you've read, meetings you've attended — all instantly accessible through natural conversation.

That's what Cosmos delivers.

As a builder and AI automation specialist, I found myself constantly switching between tools, searching through folders, and trying to remember where I stored critical information. We're drowning in knowledge yet can't access it when we need it.

The traditional approach is broken:

- Google Drive for documents (but you need to remember filenames)
- YouTube for video content (but you can't search inside videos easily)
- Meeting notes scattered across apps (with insights lost forever)
- Different tools with different interfaces (each with its own learning curve)

Cosmos solves this by bringing everything into one elegant system with a beautiful UI that makes your knowledge work for you.

### What Makes Cosmos Different

Unlike typical chatbots or basic RAG implementations, Cosmos:

1. **Handles multiple knowledge domains** — Switch contexts with a single click to access information related to specific topics (AI News, AI Tools, Personal Business)
2. **Processes any content format** — Automatically transcribes YouTube videos, extracts text from PDFs, processes spreadsheets, and more
3. **Intelligently routes information** — Uses AI to determine which knowledge base content belongs to, ensuring you get relevant answers
4. **Creates semantic connections** — Understands relationships between different pieces of information, even across formats
5. **Gets smarter over time** — Remembers your interactions and improves with use

### Who This System Is For

Cosmos is perfect for:

- **Subject matter experts** who need to organize and access their specialized knowledge
- **Consultants and agencies** juggling multiple clients and projects
- **Knowledge workers** tired of context-switching between tools
- **Teams** who need shared access to institutional knowledge
- **Anyone** who wants to leverage AI without losing control of their data

### What You'll Build

By following this guide, you'll create:

- A **custom web application** built with Lovable that provides a clean, intuitive interface
- **Multiple specialized knowledge bases** in Pinecone that organize information by domain
- **Automated workflows** in n8n that process, classify, and store your content
- **Integration points** for YouTube, Google Drive, file uploads, and more
- A **personal assistant** that understands YOUR context, not generic web knowledge

The result is a system that feels like magic — ask a question about anything in your knowledge base and get an instant, relevant response based on YOUR information.

![Screenshot 2025-05-06 at 11.32.03 AM.png](attachment:87fa6ed4-232a-4984-8afe-529a575e5e46:Screenshot_2025-05-06_at_11.32.03_AM.png)

![Screenshot 2025-05-06 at 11.07.46 AM.png](attachment:2b9a171c-463d-4e14-9c6f-91cfa810bcb8:Screenshot_2025-05-06_at_11.07.46_AM.png)

1. **Frontend (Lovable)** — The beautiful UI where you interact with the system
2. **Workflow Automation (n8n)** — The engine that processes inputs and orchestrates responses
3. **Vector Database (Pinecone)** — Where your knowledge embeddings are stored in organized namespaces
4. **PostgreSQL Database (Supabase)** — Stores system configuration, prompts, and chat types
5. **AI Brain (OpenAI)** — Provides the intelligence through GPT-4o and embeddings
6. **Memory System (Zep)** — Manages conversation history with semantic search capabilities

When you ask a question through the UI, the system:

1. Routes your question to n8n via webhooks
2. Identifies which knowledge domain you're asking about
3. Retrieves the appropriate system prompt from Supabase
4. Searches the relevant namespace in Pinecone for similar content
5. Combines the context with your question
6. Generates a response using GPT-4o
7. Returns it to your UI

All of this happens in seconds, creating a seamless experience.

Now, let's start building your system component by component.

START BY UPLOADING THE JSON

# Step 1: Setting Up Your Database in Supabase

Supabase provides the PostgreSQL backbone for your system prompts and configuration.

### Create a Supabase Account & Project

1. Go to [Supabase](https://supabase.io/) and sign up for an account
2. Create a new project with a name like "cosmos-rag-agent"
3. Note your database credentials - you'll need these for n8n
4. Make sure to replace the UUID with your own user ID from Supabase.

### Set Up Database Tables

Connect to your Supabase SQL Editor and run the following SQL commands to create the necessary tables:

```sql

sql
-- Create the table for system prompts with updated CHECK constraint
CREATE TABLE system_prompts (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    type text NOT NULL CHECK (type IN ('Personal Business', 'AI Prompts', 'AI News', 'AI Tools')),
    prompt text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL
);

-- Add comments to the table and columns for clarity
COMMENT ON TABLE system_prompts IS 'Stores system prompts used to configure AI agent behavior based on context.';
COMMENT ON COLUMN system_prompts.type IS 'The context type determining the knowledge base and behavior.';
COMMENT ON COLUMN system_prompts.prompt IS 'The detailed system message/instructions for the AI agent.';
COMMENT ON COLUMN system_prompts.created_at IS 'Timestamp when the prompt was created.';

-- Create the chat types table
CREATE TABLE chat_types (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    name text NOT NULL,
    type text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL
);

```

### Insert Initial System Prompts

Each knowledge domain needs a specialized system prompt. Insert the following prompts:

```sql

sql
-- Insert the system prompt for Personal Business
INSERT INTO system_prompts (type, prompt)
VALUES (
    'Personal Business',
    'You are a helpful assistant specializing in analyzing and answering questions based on a private collection of personal business documents. Your knowledge base consists of text documents (like notes, extracted PDFs, DOCX files) and potentially tabular data (CSVs, Excel sheets) stored within the ''Personal Business'' vector store and associated data tables.

Your primary goal is to provide accurate answers derived *only* from this specific knowledge base.

Instructions:
1.  **Prioritize RAG:** Always start by performing Retrieval-Augmented Generation (RAG) against the ''Personal Business'' vector store to find relevant information for the user''s query.
2.  **Handle Tabular Data:** If the question explicitly requires calculations (sum, average, max, min), specific row lookups, or filtering on structured data that RAG might handle unreliably, use your tools to query the relevant tabular data sources associated with the ''Personal Business'' context. Confirm if such tools are available before attempting.
3.  **Document Exploration (If RAG Fails):** If the initial RAG search yields no relevant results, use your tools to list available documents within the ''Personal Business'' knowledge base. Identify potentially relevant documents based on titles or metadata, extract their content, and then attempt to answer the question based on that extracted text.
4.  **Be Honest:** If, after attempting RAG and targeted document analysis (and potential tabular queries), you cannot find a relevant answer within the provided ''Personal Business'' knowledge base, clearly state that the information is not available in your current documents. Do *not* invent answers or use external knowledge.'
);

-- Insert the system prompt for AI Prompts
INSERT INTO system_prompts (type, prompt)
VALUES (
    'AI Prompts',
    'You are a specialized AI assistant focused on AI Prompt Engineering. Your knowledge base consists *only* of information stored within the **''AI Prompts''** vector store, containing examples, best practices, and guides related to creating effective prompts for AI models.

Your primary goal is to provide accurate answers and guidance derived *exclusively* from this specific knowledge base.

Instructions:
1.  **Prioritize RAG:** Always start by performing Retrieval-Augmented Generation (RAG) against the **''AI Prompts''** vector store to find relevant examples or guidance for the user''s query about prompts.
2.  **Document Exploration (If RAG Fails):** If the initial RAG search yields no relevant results, use your tools (if available) to list document metadata within the **''AI Prompts''** store. Identify potentially relevant documents, extract their content, and then attempt to answer the question based on that extracted text.
3.  **Focus on Provided Knowledge:** Your answers should strictly rely on the content within the **''AI Prompts''** store. Do *not* use external knowledge or information from other vector stores (like AI News or AI Tools).
4.  **Be Honest:** If, after searching the designated **''AI Prompts''** vector store, you cannot find a relevant answer or example, clearly state that the information is not available within your current knowledge base regarding AI Prompts. Do *not* invent answers.'
);

-- Insert the system prompt for AI News
INSERT INTO system_prompts (type, prompt)
VALUES (
    'AI News',
    'You are an AI assistant dedicated to providing information about recent developments and news in the field of Artificial Intelligence. Your knowledge base is sourced *exclusively* from the **''AI News''** vector store, which contains summaries and articles about AI breakthroughs, research, events, and industry trends.

Your primary goal is to provide accurate and up-to-date news-related answers based *only* on the information contained within this specific vector store.

Instructions:
1.  **Prioritize RAG:** Always start by performing Retrieval-Augmented Generation (RAG) against the **''AI News''** vector store to find relevant news items or developments related to the user''s query.
2.  **Document Exploration (If RAG Fails):** If the initial RAG search yields no relevant results, use your tools (if available) to list document metadata within the **''AI News''** store. Identify potentially relevant articles, extract their content, and then attempt to answer the question based on that extracted text.
3.  **Focus on Provided Knowledge:** Your answers should strictly rely on the content within the **''AI News''** store. Do *not* use external knowledge, real-time web search (unless equipped with a specific tool for it), or information from other vector stores (like AI Prompts or AI Tools).
4.  **Be Honest:** If, after searching the designated **''AI News''** vector store, you cannot find relevant information on the topic, clearly state that the specific news or development is not available within your current AI News knowledge base. Do *not* invent answers.'
);

-- Insert the system prompt for AI Tools
INSERT INTO system_prompts (type, prompt)
VALUES (
    'AI Tools',
    'You are an AI assistant focused on providing information about various AI tools, platforms, libraries, and software. Your knowledge base consists *solely* of information stored within the **''AI Tools''** vector store, covering details about functionalities, use cases, developers, and comparisons of different AI tools.

Your primary goal is to provide accurate descriptions and information about AI tools derived *only* from this specific knowledge base.

Instructions:
1.  **Prioritize RAG:** Always start by performing Retrieval-Augmented Generation (RAG) against the **''AI Tools''** vector store to find relevant information about the specific tool(s) or tool categories mentioned in the user''s query.
2.  **Document Exploration (If RAG Fails):** If the initial RAG search yields no relevant results, use your tools (if available) to list document metadata within the **''AI Tools''** store. Identify potentially relevant tool descriptions or documents, extract their content, and then attempt to answer the question based on that extracted text.
3.  **Focus on Provided Knowledge:** Your answers should strictly rely on the content within the **''AI Tools''** store. Do *not* use external knowledge or information from other vector stores (like AI News or AI Prompts).
4.  **Be Honest:** If, after searching the designated **''AI Tools''** vector store, you cannot find information about a specific tool or feature, clearly state that the information is not available within your current AI Tools knowledge base. Do *not* invent answers or speculate.'
);

```

### Insert Chat Types

Now add the chat type options that will appear in your UI:

```sql

sql
-- Insert chat type for AI News
INSERT INTO chat_types (user_id, name, type)
VALUES (
    'YOUR_USER_ID_HERE',-- Replace with your user ID
    'AI News',
    'AI News'
);

-- Insert chat type for AI Prompts
INSERT INTO chat_types (user_id, name, type)
VALUES (
    'YOUR_USER_ID_HERE',
    'AI Prompts',
    'AI Prompts'
);

-- Insert chat type for AI Tools
INSERT INTO chat_types (user_id, name, type)
VALUES (
    'YOUR_USER_ID_HERE',
    'AI Tools',
    'AI Tools'
);

-- Insert chat type for Personal Business
INSERT INTO chat_types (user_id, name, type)
VALUES (
    'YOUR_USER_ID_HERE',
    'Personal Business',
    'Personal Business'
);

```

Make sure to replace the UUID with your own user ID from Supabase.


# Step 2: Setting Up Pinecone Vector Database

Pinecone will store your knowledge embeddings in different namespaces.

### Create Pinecone Account & Index

1. Sign up at [Pinecone](https://www.pinecone.io/)
2. Create a new index named "cosmos" with the following settings:
    - Dimensions: 1536 (matches OpenAI's embedding size)
    - Metric: Cosine (best for semantic similarity)
    - Model: text-embedding-3-small
3. Note your API key from the Pinecone dashboard - you'll need this for n8n

The index will automatically create namespaces for different knowledge domains:

- ai_news
- ai_prompts
- ai_tools
- personal_business


# Step 3: Setting Up n8n Workflow

n8n orchestrates the entire system, handling data processing, classification, and interactions.

### Create n8n Account

1. Sign up for [n8n Cloud](https://www.n8n.io/) or set up a self-hosted instance
2. Create a new workflow

### Import the Cosmos Workflow

1. Download the provided `Cosmos_RAG_Agent.json` file
2. In n8n, go to Workflows → Import From File
3. Upload the JSON file

### Configure Credentials

Set up the following API credentials in n8n:

1. **OpenAI API**
    - Create an API key at [OpenAI](https://platform.openai.com/)
    - In n8n, go to Settings → Credentials
    - Add a new credential for OpenAI with your API key
2. **Pinecone API**
    - Use the API key from your Pinecone dashboard
    - Add a new credential for Pinecone
3. **Supabase API**
    - From your Supabase project dashboard, get:
        - Project URL
        - API key (under Project Settings → API)
    - Add a new credential for Supabase
4. **Zep API** (for memory)
    - Set up a Zep instance or sign up for a cloud account
    - Get your API key
    - Add a new credential for Zep
5. **RapidAPI** (for YouTube transcripts)
    - Sign up at [RapidAPI](https://rapidapi.com/)
    - Subscribe to the YouTube Transcript API
    - Add your API key to the Transcribe node
6. **Tavily API** (for news search)
    - Get an API key from [Tavily](https://tavily.com/)
    - Update the Tavily HTTP Request node with your key
7. **Google Drive** (optional)
    - Set up OAuth2 credentials if you want Google Drive integration

### Configure Webhooks

The workflow uses several webhooks to handle different types of requests:

1. Main chat webhook - `/chat`
2. YouTube processing webhook - `/youtube`
3. News search webhook - `/news`
4. File upload webhook - `/fileupload`



# Step 4: Setting Up the Lovable Frontend

Lovable provides the beautiful UI for your Cosmos system.

### Create Lovable Account

1. Sign up at [Lovable](https://lovable.app/)
2. Create a new project named "Cosmos"

### Design Your UI

The basic UI needs:

1. A chat interface
2. A selector for different knowledge bases (AI News, AI Prompts, etc.)
3. Input options for:
    - YouTube URLs
    - News queries
    - File uploads

You can design this from scratch or use the provided template.

Lovable Link → [Your deployed app URL will go here]

### Connect to Webhooks

Configure the following webhook connections in your Lovable app:

1. Chat functionality - connect to the `/chat` webhook
2. YouTube processing - connect to the `/youtube` webhook
3. News search - connect to the `/news` webhook
4. File uploads - connect to the `/fileupload` webhook

Use the complete webhook URLs from your n8n setup.

### Deploy Your App

Once configured, deploy your Lovable app to get a public URL that you can access from anywhere.


# Step 5: Testing Your System

Now that everything is set up, it's time to test your Cosmos RAG Agent:

### Basic Chat Test

1. Open your Lovable app
2. Select a knowledge base (e.g., "AI News")
3. Type a simple question like "What's new in AI?"
4. Verify that you get a meaningful response

### YouTube Integration Test

1. Find a YouTube video about AI
2. Copy the URL
3. Paste it into the YouTube URL input
4. After processing (you'll see "I feel stronger now"), ask a question about the content
5. Verify that the system provides information from the video

### File Upload Test

1. Prepare a PDF, text file, or other document
2. Upload it through the file upload interface
3. After processing, ask questions about the document content
4. Verify that the system correctly extracts and uses the information

### News Search Test

1. Enter a news query like "AI ethics" or "large language models"
2. After processing, ask follow-up questions about the topic
3. Verify that the system returns relevant information from recent news


# Step 6: Customization

Now that your basic system is working, you can customize it:

### Add New Knowledge Domains

To add a new domain (e.g., "Marketing"):

1. Add a new system prompt in Supabase:

```sql

sql
INSERT INTO system_prompts (type, prompt)
VALUES (
    'Marketing',
    'You are a marketing assistant...'
);

```

1. Add a new chat type:

```sql

sql
INSERT INTO chat_types (user_id, name, type)
VALUES ('your-user-id', 'Marketing', 'Marketing');

```

1. Update the text classifier in n8n to recognize marketing content

### Customize the UI

Modify your Lovable frontend to:

- Match your branding
- Add additional features
- Improve the user experience

### Extend Functionality

Consider adding:

- Authentication for multiple users
- Additional data sources
- Integration with other tools like Slack or Email

## Troubleshooting Common Issues

### Classification Not Working

If content isn't being classified correctly:

- Check the Text Classifier node in n8n
- Make sure your categories are correctly defined
- Test with simple, clearly defined content first

### Vector Store Issues

If RAG retrieval isn't working:

- Verify your Pinecone credentials
- Check that namespaces are correctly set up
- Make sure embeddings are being generated properly

### Frontend Connection Problems

If your UI isn't connecting to n8n:

- Double-check all webhook URLs
- Ensure CORS settings allow your frontend to connect
- Test webhooks directly using a tool like Postman

### Memory Not Functioning

If the conversation memory isn't working:

- Check your Zep configuration
- Verify the Zep node in n8n is correctly set up
- Make sure conversation IDs are being maintained


# Next Steps & Advanced Features

Once your basic Cosmos system is working, consider these enhancements:

### Multi-User Support

Modify the system to support multiple users with separate knowledge bases and permissions.

### Automated Content Discovery

Set up scheduled workflows to automatically process:

- New YouTube videos from specific channels
- RSS feeds for news and blog posts
- Google Drive folders for new documents

### Custom Agents for Specific Tasks

Create specialized agents for:

- Research on specific topics
- Content creation and drafting
- Meeting summaries and action items

### Integration with Additional Tools

Connect your Cosmos system to:

- Slack for team-wide access
- Email for sending reports and summaries
- Calendar for meeting preparation