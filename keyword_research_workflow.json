{"name": "keyword_research_workflow", "nodes": [{"parameters": {}, "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [880, -180], "id": "c0f3ac00-b800-49d0-adb3-84ab6b7a7b0f"}, {"parameters": {"values": {"string": [{"name": "seedKeyword", "value": "ai music generator"}]}, "options": {}}, "name": "Set Seed Keyword", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1080, -180], "id": "e043b817-a676-4c55-9d01-6c95168eec8a"}, {"parameters": {"url": "https://api.dataforseo.com/v3/keywords_data/google_ads/search_volume/task_post", "options": {}}, "name": "HTTP Request to Keyword API", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1320, -180], "id": "b15807a3-d4f4-4653-b0be-30230fcdc5fc"}, {"parameters": {"functionCode": "// Mock data for testing\nconst seedKeyword = $input.item.json.bodyParameters.parameters[0].value[0];\nreturn [\n  {\n    json: {\n      keyword: seedKeyword,\n      search_volume: 450,\n      cpc: 1.75,\n      competition: 0.62\n    }\n  },\n  {\n    json: {\n      keyword: seedKeyword + \" free\",\n      search_volume: 320,\n      cpc: 0.95,\n      competition: 0.48\n    }\n  },\n  {\n    json: {\n      keyword: seedKeyword + \" online\",\n      search_volume: 210,\n      cpc: 1.25,\n      competition: 0.55\n    }\n  },\n  {\n    json: {\n      keyword: \"best \" + seedKeyword,\n      search_volume: 180,\n      cpc: 2.10,\n      competition: 0.72\n    }\n  }\n];"}, "name": "Extract Keywords", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1520, -180], "id": "6306ca09-ce8c-4264-a78a-8f99e1549a84"}, {"parameters": {"conditions": {"number": [{"value1": "={{ $json[\"search_volume\"] }}", "operation": "larger", "value2": 100}]}}, "name": "Filter Volume", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1720, -180], "id": "78b00c55-e20c-4164-902e-79a788c787cc"}, {"parameters": {"functionCode": "// Prepare data for GPT\nconst keywordData = $input.all().map(item => {\n  return {\n    keyword: item.json.keyword,\n    volume: item.json.search_volume,\n    cpc: item.json.cpc\n  };\n});\n\nreturn [{ json: { keywordData } }];"}, "name": "Prepare for GPT", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [880, 120], "id": "e317f3a7-894a-4279-a964-4ec586cc663c"}, {"parameters": {"operation": "completion", "requestOptions": {}}, "name": "GPT Clustering", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [1080, 120], "id": "e8451c5d-32f3-43cd-96d7-5680fdcc2fdb"}, {"parameters": {"resource": "spreadsheet", "title": "={{ \"Keyword Research - \" + $node[\"Set Seed Keyword\"].json.seedKeyword }}", "options": {}}, "name": "Create Google Sheet", "type": "n8n-nodes-base.googleSheets", "typeVersion": 1, "position": [1520, 120], "id": "0b88d01b-0694-4f70-a305-8db5d5de6d16"}, {"parameters": {"operation": "append", "sheetId": "={{ $json.spreadsheetId }}", "range": "A:A", "options": {}}, "name": "Write to Sheet", "type": "n8n-nodes-base.googleSheets", "typeVersion": 1, "position": [1720, 120], "id": "d5cf4de7-b4d5-4f59-bddd-ee381c8d38f1"}, {"parameters": {"functionCode": "const response = $json.choices[0].message.content;\nreturn [{ json: { result: response } }];"}, "id": "110cee95-11e3-401d-be09-650110b103a5", "name": "Format GPT Response", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1320, 120]}, {"parameters": {"content": "## Input Setup", "height": 280, "width": 420, "color": 2}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [820, -260], "id": "57a774ee-2981-4420-86d8-4c1b681584f5", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "## API Call & Filter:", "height": 280, "width": 620, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1260, -260], "id": "f876b7e4-039d-4981-bcc2-5c6aab1a8df1", "name": "Sticky Note1"}, {"parameters": {"content": "## AI Analysis", "height": 280, "width": 420, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [820, 40], "id": "e4ebbe72-acad-449c-8bb9-13b6eb53fea1", "name": "Sticky Note2"}, {"parameters": {"content": "## Sheet Output", "height": 280, "width": 620, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1260, 40], "id": "6d1e5b8a-81ac-439d-9dba-8e17388ddf47", "name": "Sticky Note3"}, {"parameters": {"content": "## Notify", "height": 580, "width": 280, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1900, -260], "id": "998237b9-5c2c-421d-99a9-edfe9c902bd0", "name": "Sticky Note4"}, {"parameters": {"otherOptions": {}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [1980, -200], "id": "3a73eca6-7d14-4808-a064-fd7ef8658d46", "name": "<PERSON><PERSON>ck", "webhookId": "7e57d4e9-fbc1-4569-95f7-d7542d00f6c3"}, {"parameters": {"additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1980, -20], "id": "835800a6-7fef-4b5e-acdf-92e01ed2b96d", "name": "Telegram", "webhookId": "289e0da0-21db-4006-8230-f523f19bd2d3", "credentials": {"telegramApi": {"id": "YOUR_TELEGRAM_CREDENTIAL_ID", "name": "Telegram Account"}}}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [1980, 160], "id": "e3fbbbab-2afd-4974-8edd-86f5b4ee2ad8", "name": "Gmail", "webhookId": "b9995aa6-0b28-4e6e-891e-392f84520dc5", "credentials": {"gmailOAuth2": {"id": "YOUR_GMAIL_CREDENTIAL_ID", "name": "Gmail Account"}}}], "pinData": {}, "connections": {"Manual Trigger": {"main": [[{"node": "Set Seed Keyword", "type": "main", "index": 0}]]}, "Set Seed Keyword": {"main": [[{"node": "HTTP Request to Keyword API", "type": "main", "index": 0}]]}, "HTTP Request to Keyword API": {"main": [[{"node": "Extract Keywords", "type": "main", "index": 0}]]}, "Extract Keywords": {"main": [[{"node": "Filter Volume", "type": "main", "index": 0}]]}, "Filter Volume": {"main": [[{"node": "Prepare for GPT", "type": "main", "index": 0}]]}, "Prepare for GPT": {"main": [[{"node": "GPT Clustering", "type": "main", "index": 0}]]}, "GPT Clustering": {"main": [[{"node": "Format GPT Response", "type": "main", "index": 0}]]}, "Create Google Sheet": {"main": [[{"node": "Write to Sheet", "type": "main", "index": 0}]]}, "Format GPT Response": {"main": [[{"node": "Create Google Sheet", "type": "main", "index": 0}]]}, "Write to Sheet": {"main": [[{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}, {"node": "Telegram", "type": "main", "index": 0}, {"node": "Gmail", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "22abda56-4f98-4c47-b488-663059e6e709", "meta": {"instanceId": "9b52bd59153ae82dd3f85ba55bba63a67709d765ac7eca1262e072ce1b3e6ba8"}, "id": "aoYT5Takw6ZxcF5p", "tags": []}